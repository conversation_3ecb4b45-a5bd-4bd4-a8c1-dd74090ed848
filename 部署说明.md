# 日志分析工具部署说明

## 文件说明
- `日志分析工具.exe` - 主程序文件（约7.3MB）
- 这是一个独立的可执行文件，包含了完整的Python运行环境

## 系统要求
- **操作系统**: Windows 7/8/10/11 (64位)
- **内存**: 至少512MB可用内存
- **磁盘空间**: 至少20MB可用空间（程序运行时会临时解压文件）

## 部署步骤
1. 将 `日志分析工具.exe` 复制到目标电脑的任意文件夹
2. 双击运行即可使用

## 可能遇到的问题及解决方案

### 问题1: 提示缺少dll文件
**解决方案**: 安装 Microsoft Visual C++ Redistributable
- 下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe
- 安装后重新运行程序

### 问题2: 程序启动缓慢
**原因**: 首次运行时需要解压临时文件
**解决方案**: 这是正常现象，后续运行会更快

### 问题3: 杀毒软件误报
**原因**: PyInstaller打包的exe可能被误认为病毒
**解决方案**: 将程序添加到杀毒软件白名单

### 问题4: 无法读取中文路径的日志文件
**解决方案**: 确保日志文件路径不包含特殊字符，或将日志文件放在英文路径下

## 测试建议
在部署到生产环境前，建议：
1. 在目标电脑上创建测试日志文件
2. 运行程序进行功能测试
3. 验证各种编码格式的日志文件都能正常读取

## 技术支持
如遇到问题，请提供：
1. 操作系统版本
2. 错误信息截图
3. 日志文件示例（如果涉及文件读取问题）
