('E:\\log_analyzer\\dist\\日志分析工具.exe',
 True,
 False,
 False,
 'D:\\software\\python31210\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\log_analyzer\\build\\日志分析工具\\日志分析工具.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'E:\\log_analyzer\\build\\日志分析工具\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\software\\python31210\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\software\\python31210\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('log_analyzer', 'E:\\log_analyzer\\log_analyzer.py', 'PYSOURCE'),
  ('python312.dll', 'D:\\software\\python31210\\python312.dll', 'BINARY'),
  ('unicodedata.pyd',
   'D:\\software\\python31210\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\software\\python31210\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\software\\python31210\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\software\\python31210\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\software\\python31210\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\software\\python31210\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\software\\python31210\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\software\\python31210\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll',
   'D:\\software\\python31210\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\log_analyzer\\build\\日志分析工具\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1754555302,
 [('run.exe',
   'D:\\software\\python31210\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'D:\\software\\python31210\\python312.dll')
