(['E:\\log_analyzer\\log_analyzer.py'],
 ['E:\\log_analyzer'],
 [],
 [('D:\\software\\python31210\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\software\\python31210\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\software\\python31210\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\software\\python31210\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('log_analyzer', 'E:\\log_analyzer\\log_analyzer.py', 'PYSOURCE')],
 [('zipfile',
   'D:\\software\\python31210\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\software\\python31210\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\software\\python31210\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'D:\\software\\python31210\\Lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\software\\python31210\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib', 'D:\\software\\python31210\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ipaddress', 'D:\\software\\python31210\\Lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'D:\\software\\python31210\\Lib\\fnmatch.py', 'PYMODULE'),
  ('contextlib', 'D:\\software\\python31210\\Lib\\contextlib.py', 'PYMODULE'),
  ('argparse', 'D:\\software\\python31210\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\software\\python31210\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\software\\python31210\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\software\\python31210\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'D:\\software\\python31210\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\software\\python31210\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\software\\python31210\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\software\\python31210\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\software\\python31210\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\software\\python31210\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\software\\python31210\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\software\\python31210\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\software\\python31210\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\software\\python31210\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\software\\python31210\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\software\\python31210\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\software\\python31210\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\software\\python31210\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\software\\python31210\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\software\\python31210\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\software\\python31210\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\software\\python31210\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\software\\python31210\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\software\\python31210\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\software\\python31210\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'D:\\software\\python31210\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\software\\python31210\\Lib\\bisect.py', 'PYMODULE'),
  ('importlib._abc',
   'D:\\software\\python31210\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\software\\python31210\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\software\\python31210\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\software\\python31210\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\software\\python31210\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\software\\python31210\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\software\\python31210\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\software\\python31210\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\software\\python31210\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\software\\python31210\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\software\\python31210\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\software\\python31210\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'D:\\software\\python31210\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\software\\python31210\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\software\\python31210\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\software\\python31210\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\software\\python31210\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\software\\python31210\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\software\\python31210\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\software\\python31210\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\software\\python31210\\Lib\\calendar.py', 'PYMODULE'),
  ('socket', 'D:\\software\\python31210\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\software\\python31210\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\software\\python31210\\Lib\\quopri.py', 'PYMODULE'),
  ('email', 'D:\\software\\python31210\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\software\\python31210\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\software\\python31210\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\software\\python31210\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\software\\python31210\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\software\\python31210\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\software\\python31210\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\software\\python31210\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\software\\python31210\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\software\\python31210\\Lib\\bz2.py', 'PYMODULE'),
  ('_strptime', 'D:\\software\\python31210\\Lib\\_strptime.py', 'PYMODULE'),
  ('threading', 'D:\\software\\python31210\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\software\\python31210\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'D:\\software\\python31210\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\software\\python31210\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\software\\python31210\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\software\\python31210\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\software\\python31210\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\software\\python31210\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\software\\python31210\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\software\\python31210\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\software\\python31210\\Lib\\ast.py', 'PYMODULE'),
  ('stringprep', 'D:\\software\\python31210\\Lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'D:\\software\\python31210\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\software\\python31210\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('subprocess', 'D:\\software\\python31210\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\software\\python31210\\Lib\\signal.py', 'PYMODULE'),
  ('datetime', 'D:\\software\\python31210\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\software\\python31210\\Lib\\_pydatetime.py',
   'PYMODULE')],
 [('python312.dll', 'D:\\software\\python31210\\python312.dll', 'BINARY'),
  ('unicodedata.pyd',
   'D:\\software\\python31210\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\software\\python31210\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\software\\python31210\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\software\\python31210\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\software\\python31210\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\software\\python31210\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\software\\python31210\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\software\\python31210\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll',
   'D:\\software\\python31210\\DLLs\\libcrypto-3.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'E:\\log_analyzer\\build\\日志分析工具\\base_library.zip',
   'DATA')],
 [('_weakrefset', 'D:\\software\\python31210\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\software\\python31210\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\software\\python31210\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\software\\python31210\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\software\\python31210\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\software\\python31210\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\software\\python31210\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\software\\python31210\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\software\\python31210\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\software\\python31210\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\software\\python31210\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\software\\python31210\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\software\\python31210\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\software\\python31210\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\software\\python31210\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\software\\python31210\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\software\\python31210\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\software\\python31210\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\software\\python31210\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\software\\python31210\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\software\\python31210\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\software\\python31210\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\software\\python31210\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\software\\python31210\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\software\\python31210\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\software\\python31210\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\software\\python31210\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\software\\python31210\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\software\\python31210\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\software\\python31210\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\software\\python31210\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\software\\python31210\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\software\\python31210\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\software\\python31210\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\software\\python31210\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\software\\python31210\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\software\\python31210\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\software\\python31210\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\software\\python31210\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\software\\python31210\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\software\\python31210\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\software\\python31210\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\software\\python31210\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\software\\python31210\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\software\\python31210\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\software\\python31210\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\software\\python31210\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\software\\python31210\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\software\\python31210\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\software\\python31210\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\software\\python31210\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\software\\python31210\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\software\\python31210\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\software\\python31210\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\software\\python31210\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\software\\python31210\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\software\\python31210\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\software\\python31210\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\software\\python31210\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\software\\python31210\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\software\\python31210\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\software\\python31210\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\software\\python31210\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\software\\python31210\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\software\\python31210\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\software\\python31210\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\software\\python31210\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\software\\python31210\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\software\\python31210\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\software\\python31210\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\software\\python31210\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\software\\python31210\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\software\\python31210\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\software\\python31210\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\software\\python31210\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\software\\python31210\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\software\\python31210\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\software\\python31210\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\software\\python31210\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\software\\python31210\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\software\\python31210\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\software\\python31210\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\software\\python31210\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\software\\python31210\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\software\\python31210\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\software\\python31210\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\software\\python31210\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\software\\python31210\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\software\\python31210\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\software\\python31210\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\software\\python31210\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\software\\python31210\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\software\\python31210\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\software\\python31210\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\software\\python31210\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\software\\python31210\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\software\\python31210\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\software\\python31210\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\software\\python31210\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\software\\python31210\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\software\\python31210\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\software\\python31210\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\software\\python31210\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\software\\python31210\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\software\\python31210\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\software\\python31210\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\software\\python31210\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\software\\python31210\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\software\\python31210\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('weakref', 'D:\\software\\python31210\\Lib\\weakref.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\software\\python31210\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\software\\python31210\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('operator', 'D:\\software\\python31210\\Lib\\operator.py', 'PYMODULE'),
  ('reprlib', 'D:\\software\\python31210\\Lib\\reprlib.py', 'PYMODULE'),
  ('functools', 'D:\\software\\python31210\\Lib\\functools.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\software\\python31210\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('enum', 'D:\\software\\python31210\\Lib\\enum.py', 'PYMODULE'),
  ('genericpath', 'D:\\software\\python31210\\Lib\\genericpath.py', 'PYMODULE'),
  ('heapq', 'D:\\software\\python31210\\Lib\\heapq.py', 'PYMODULE'),
  ('sre_compile', 'D:\\software\\python31210\\Lib\\sre_compile.py', 'PYMODULE'),
  ('copyreg', 'D:\\software\\python31210\\Lib\\copyreg.py', 'PYMODULE'),
  ('re._parser', 'D:\\software\\python31210\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants',
   'D:\\software\\python31210\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\software\\python31210\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\software\\python31210\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('linecache', 'D:\\software\\python31210\\Lib\\linecache.py', 'PYMODULE'),
  ('abc', 'D:\\software\\python31210\\Lib\\abc.py', 'PYMODULE'),
  ('traceback', 'D:\\software\\python31210\\Lib\\traceback.py', 'PYMODULE'),
  ('posixpath', 'D:\\software\\python31210\\Lib\\posixpath.py', 'PYMODULE'),
  ('stat', 'D:\\software\\python31210\\Lib\\stat.py', 'PYMODULE'),
  ('io', 'D:\\software\\python31210\\Lib\\io.py', 'PYMODULE'),
  ('locale', 'D:\\software\\python31210\\Lib\\locale.py', 'PYMODULE'),
  ('keyword', 'D:\\software\\python31210\\Lib\\keyword.py', 'PYMODULE'),
  ('sre_parse', 'D:\\software\\python31210\\Lib\\sre_parse.py', 'PYMODULE'),
  ('warnings', 'D:\\software\\python31210\\Lib\\warnings.py', 'PYMODULE'),
  ('codecs', 'D:\\software\\python31210\\Lib\\codecs.py', 'PYMODULE'),
  ('types', 'D:\\software\\python31210\\Lib\\types.py', 'PYMODULE'),
  ('ntpath', 'D:\\software\\python31210\\Lib\\ntpath.py', 'PYMODULE'),
  ('os', 'D:\\software\\python31210\\Lib\\os.py', 'PYMODULE'),
  ('collections',
   'D:\\software\\python31210\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('re', 'D:\\software\\python31210\\Lib\\re\\__init__.py', 'PYMODULE')])
