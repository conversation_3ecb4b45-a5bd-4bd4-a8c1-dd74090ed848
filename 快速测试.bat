@echo off
chcp 65001 >nul
echo ================================
echo 日志分析工具快速测试
echo ================================
echo.

REM 创建测试日志文件
echo 2024-08-07 10:30:15 INFO: 系统启动成功 > 测试日志.log
echo 2024-08-07 10:30:16 DEBUG: 加载配置文件 >> 测试日志.log
echo 2024-08-07 10:30:17 ERROR: 连接数据库失败 >> 测试日志.log
echo 2024-08-07 10:30:18 WARN: 重试连接数据库 >> 测试日志.log
echo 2024-08-07 10:30:19 INFO: 数据库连接成功 >> 测试日志.log
echo 2024-08-07 10:30:20 ERROR: 用户认证失败 >> 测试日志.log

echo 已创建测试日志文件: 测试日志.log
echo.
echo 现在将启动日志分析工具...
echo 请按照提示操作：
echo 1. 选择 "1" (分析单个文件)
echo 2. 输入文件路径: 测试日志.log
echo 3. 输入关键字: ERROR INFO
echo.
pause

REM 启动程序
"日志分析工具.exe"

echo.
echo 测试完成！
pause

REM 清理测试文件
del 测试日志.log 2>nul
