('E:\\log_analyzer\\build\\日志分析工具\\PYZ-00.pyz',
 [('_compat_pickle',
   'D:\\software\\python31210\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\software\\python31210\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\software\\python31210\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\software\\python31210\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\software\\python31210\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\software\\python31210\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\software\\python31210\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\software\\python31210\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\software\\python31210\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\software\\python31210\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\software\\python31210\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\software\\python31210\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\software\\python31210\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\software\\python31210\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\software\\python31210\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\software\\python31210\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\software\\python31210\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'D:\\software\\python31210\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\software\\python31210\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\software\\python31210\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\software\\python31210\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\software\\python31210\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\software\\python31210\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\software\\python31210\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\software\\python31210\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\software\\python31210\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\software\\python31210\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\software\\python31210\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\software\\python31210\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\software\\python31210\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\software\\python31210\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\software\\python31210\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\software\\python31210\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\software\\python31210\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\software\\python31210\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\software\\python31210\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\software\\python31210\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\software\\python31210\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\software\\python31210\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\software\\python31210\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\software\\python31210\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\software\\python31210\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\software\\python31210\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\software\\python31210\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\software\\python31210\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\software\\python31210\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\software\\python31210\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'D:\\software\\python31210\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\software\\python31210\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\software\\python31210\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\software\\python31210\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\software\\python31210\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\software\\python31210\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\software\\python31210\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\software\\python31210\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\software\\python31210\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\software\\python31210\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\software\\python31210\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\software\\python31210\\Lib\\ipaddress.py', 'PYMODULE'),
  ('logging',
   'D:\\software\\python31210\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\software\\python31210\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\software\\python31210\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\software\\python31210\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\software\\python31210\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\software\\python31210\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\software\\python31210\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\software\\python31210\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\software\\python31210\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\software\\python31210\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\software\\python31210\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\software\\python31210\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\software\\python31210\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\software\\python31210\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\software\\python31210\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\software\\python31210\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\software\\python31210\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\software\\python31210\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\software\\python31210\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\software\\python31210\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\software\\python31210\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\software\\python31210\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\software\\python31210\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\software\\python31210\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\software\\python31210\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\software\\python31210\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\software\\python31210\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\software\\python31210\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\software\\python31210\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\software\\python31210\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\software\\python31210\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
