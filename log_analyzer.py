import re
from datetime import datetime
from collections import defaultdict
import os

class LogAnalyzer:
    def __init__(self):
        self.keyword_stats = defaultdict(lambda: defaultdict(list))
    
    def analyze_directory(self, directory_path, keywords, file_extensions=None):
        """
        分析目录中所有日志文件
        :param directory_path: 目录路径
        :param keywords: 要搜索的关键字列表
        :param file_extensions: 要分析的文件扩展名列表，例如 ['.log', '.txt']
        """
        if not os.path.exists(directory_path):
            print(f"错误：找不到目录 {directory_path}")
            return
        
        if not os.path.isdir(directory_path):
            print(f"错误：{directory_path} 不是一个目录")
            return

        if file_extensions is None:
            file_extensions = ['.log', '.txt']

        found_files = False
        for root, _, files in os.walk(directory_path):
            for file in files:
                if any(file.lower().endswith(ext.lower()) for ext in file_extensions):
                    found_files = True
                    full_path = os.path.join(root, file)
                    self.analyze_log(full_path, keywords)
        
        if not found_files:
            print(f"\n在目录 {directory_path} 中没有找到日志文件")

    def analyze_log(self, log_file, keywords):
        """
        分析日志文件中的关键字
        :param log_file: 日志文件路径
        :param keywords: 要搜索的关键字列表
        """
        print(f"\n正在分析日志文件: {log_file}")
        
        # 尝试使用多种编码格式读取文件
        encodings_to_try = ['utf-8', 'gbk', 'gb2312']
        file_read = False
        for encoding in encodings_to_try:
            try:
                with open(log_file, 'r', encoding=encoding) as f:
                    for line_number, line in enumerate(f, 1):
                        timestamp = self._extract_timestamp(line)
                        
                        for keyword in keywords:
                            if keyword.lower() in line.lower():
                                self.keyword_stats[log_file][keyword].append({
                                    'line_number': line_number,
                                    'timestamp': timestamp,
                                    'line': line.strip()
                                })
                print(f"成功使用 {encoding} 编码读取文件: {log_file}")
                file_read = True
                break  # 如果成功读取，则跳出循环
            except UnicodeDecodeError:
                continue  # 如果解码错误，则尝试下一种编码
            except Exception as e:
                print(f"警告：处理文件 {log_file} 时出错：{str(e)}")
                break  # 如果发生其他错误，则停止尝试

        if not file_read:
            print(f"警告：无法使用任何支持的编码读取文件 {log_file}")
    
    def _extract_timestamp(self, line):
        """
        从日志行中提取时间戳
        支持多种常见的时间格式
        """
        patterns = [
            r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',  # 2024-03-14 12:34:56
            r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}',  # 2024/03/14 12:34:56
            r'\d{2}:\d{2}:\d{2}',                     # 12:34:56
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                return match.group()
        return None
    
    def print_results(self):
        """
        打印分析结果
        """
        total_occurrences = defaultdict(int)
        
        print("\n=== 分析结果 ===")
        
        # 首先打印每个文件的详细结果
        for file_path, keywords in self.keyword_stats.items():
            print(f"\n文件: {file_path}")
            
            for keyword, occurrences in keywords.items():
                count = len(occurrences)
                total_occurrences[keyword] += count
                print(f"\n关键字 '{keyword}' 在此文件中出现 {count} 次：")
                
                if count > 0:
                    print("\n具体出现位置：")
                    for occurrence in occurrences:
                        timestamp = occurrence['timestamp'] or '无时间戳'
                        print(f"行号: {occurrence['line_number']}, 时间: {timestamp}")
                        print(f"内容: {occurrence['line']}")
                        print("-" * 80)
        
        # 打印总计信息
        print("\n=== 搜索总结 ===")
        for keyword, total_count in total_occurrences.items():
            print(f"关键字 '{keyword}' 在所有文件中共出现 {total_count} 次")

def main():
    print("=== 日志文件关键字分析工具 ===")
    
    # 选择分析模式
    while True:
        print("\n请选择分析模式：")
        print("1. 分析单个文件")
        print("2. 分析整个目录")
        choice = input("请输入选项编号 (1 或 2): ").strip()
        
        if choice in ['1', '2']:
            break
        print("错误：请输入有效的选项")
    
    # 获取文件路径或目录路径
    while True:
        if choice == '1':
            path = input("\n请输入日志文件的路径: ").strip()
            if os.path.exists(path) and os.path.isfile(path):
                break
            print("错误：找不到指定的文件，请重新输入")
        else:
            path = input("\n请输入日志文件所在的目录路径: ").strip()
            if os.path.exists(path) and os.path.isdir(path):
                break
            print("错误：找不到指定的目录，请重新输入")
    
    # 获取关键字
    while True:
        keywords_input = input("\n请输入要搜索的关键字（多个关键字用空格分隔）: ").strip()
        keywords = [k.strip() for k in keywords_input.split() if k.strip()]
        if keywords:
            break
        print("错误：请至少输入一个关键字")
    
    # 如果是目录模式，询问文件扩展名
    file_extensions = None
    if choice == '2':
        ext_input = input("\n请输入要搜索的文件扩展名（多个用空格分隔，直接回车默认为.log .txt）: ").strip()
        if ext_input:
            file_extensions = [f".{ext.strip('.')}" for ext in ext_input.split()]
    
    # 创建分析器实例并执行分析
    analyzer = LogAnalyzer()
    if choice == '1':
        analyzer.analyze_log(path, keywords)
    else:
        analyzer.analyze_directory(path, keywords, file_extensions)
    
    analyzer.print_results()
    
    # 等待用户确认后退出
    input("\n按回车键退出...")

if __name__ == '__main__':
    main()