('E:\\log_analyzer\\build\\日志分析工具\\日志分析工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'E:\\log_analyzer\\build\\日志分析工具\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\log_analyzer\\build\\日志分析工具\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\software\\python31210\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\software\\python31210\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('log_analyzer', 'E:\\log_analyzer\\log_analyzer.py', 'PYSOURCE'),
  ('python312.dll', 'D:\\software\\python31210\\python312.dll', 'BINARY'),
  ('unicodedata.pyd',
   'D:\\software\\python31210\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\software\\python31210\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\software\\python31210\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\software\\python31210\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\software\\python31210\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\software\\python31210\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\software\\python31210\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\software\\python31210\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll',
   'D:\\software\\python31210\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\log_analyzer\\build\\日志分析工具\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
